import React, { useEffect, useRef } from 'react';
import { NavigationContainer, DefaultTheme } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar, Platform, AppState } from 'react-native';
import { GameProvider } from './components/game/GameContext';
import { fetchAndUpdateGameContent } from './services/contentService';
import { loadAdSettings } from './services/adSettingsService';
import { LanguageProvider } from './services/i18nService';
import { AppSettingsProvider } from './context/AppSettingsContext';
import { AdBasedAccessProvider } from './context/AdBasedAccessContext';
import { AdSettingsProvider } from './context/AdSettingsContext';
import { initRevenueCat, getOfferings } from './services/revenueCatService';
import {
  initializePushNotificationsAtLaunch,
  handlePushNotificationAppStateChange
} from './services/pushNotificationService';
import NetInfo from '@react-native-community/netinfo';
import mobileAds from 'react-native-google-mobile-ads';

// Import screens
import HomeScreen from './screens/HomeScreen';
import SelectCategoriesScreen from './screens/SelectCategoriesScreen';
import TouchGameScreen from './screens/TouchGameScreen';
import SettingsScreen from './screens/SettingsScreen';
import WatchAdsScreen from './screens/WatchAdsScreen';
import WhatsNewScreen from './screens/WhatsNewScreen';
import * as Sentry from '@sentry/react-native';

Sentry.init({
  // Debug mode for development
  debug: __DEV__,
  
  dsn: 'https://<EMAIL>/4509471317098496',

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [Sentry.mobileReplayIntegration(), Sentry.feedbackIntegration()],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

const MyTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: '#131416', // Usar el mismo color de fondo que tu app
  },
};

// Initialize RevenueCat early
initRevenueCat();

// Initialize AdMob
mobileAds()
  .initialize()
  .then(adapterStatuses => {
    console.log('AdMob initialized successfully:', adapterStatuses);
  })
  .catch(error => {
    console.error('AdMob initialization failed:', error);
    Sentry.captureException(error);
  });

const Stack = createStackNavigator();

export default Sentry.wrap(function App() {
  // Create a navigation ref for navigating outside of components
  const navigationRef = useRef(null);

  useEffect(() => {
    // Initialize push notifications at app launch
    const initializePushNotifications = async () => {
      try {
        await initializePushNotificationsAtLaunch();
      } catch (error) {
        console.error('Error initializing push notifications:', error);
      }
    };

    // Add this to fetch and log offerings
    const fetchOfferings = async () => {
      try {
        const offerings = await getOfferings();
        console.log('RevenueCat offerings:', offerings);
      } catch (error) {
        console.error('Error fetching offerings:', error);
      }
    };

    // Initialize push notifications first
    initializePushNotifications();

    // Setup AppState listener to handle permission changes when app returns to foreground
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        console.log('🔔 [App] App became active, checking for permission changes...');
        handlePushNotificationAppStateChange().catch(error => {
          console.error('🔔 [App] Error handling app state change:', error);
        });
      }
    };

    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

    fetchOfferings();

    const loadContent = async () => {
      try {
        console.log('Initializing content and settings loading...');

        // Load both content and ad settings in parallel
        const [contentResult, adSettings] = await Promise.all([
          fetchAndUpdateGameContent(),
          loadAdSettings()
        ]);

        // Check if content is null or empty (no questions and no dares)
        const content = contentResult?.content;
        const isEmpty = content === null || (
          content &&
          Object.values(content.questions).every(arr => arr.length === 0) &&
          Object.values(content.dares).every(arr => arr.length === 0)
        );

        if (!isEmpty) {
          // Log a summary of the content loaded
          const questionCount = Object.values(content.questions)
            .reduce((sum, questions) => sum + questions.length, 0);

          const dareCount = Object.values(content.dares)
            .reduce((sum, dares) => sum + dares.length, 0);

          console.log(`Successfully loaded game content: ${questionCount} questions, ${dareCount} dares`);
        } else {
          console.warn('No content was loaded or content is empty');
        }

        // Log ad settings loading result
        if (adSettings) {
          console.log('Successfully loaded ad settings:', adSettings);
        } else {
          console.log('Using default ad settings');
        }

        // Navigate to NoInternetScreen if content is empty, regardless of ad settings
        if (isEmpty && navigationRef.current) {
          // Check network status to determine the appropriate scenario
          const networkState = await NetInfo.fetch();
          const scenario = networkState.isConnected ? 'content-failed' : 'no-internet';

          console.warn(`No valid content available, showing NoInternetScreen with scenario: ${scenario}`);
          // @ts-ignore - TS doesn't know about the navigate function on the ref
          navigationRef.current.navigate('NoInternetScreen', { scenario });
        }
      } catch (error) {
        console.error('Error initiating content loading:', error);
        Sentry.captureException(error);
      }
    };

    // Start loading content
    loadContent();

    // Setup network connectivity listener to detect when network status changes
    const unsubscribe = NetInfo.addEventListener(async state => {
      if (state.isConnected === false) {
        console.log('Network connection lost, checking for cached content');
        // Check if we have cached content
        const contentResult = await fetchAndUpdateGameContent();
        const content = contentResult?.content;

        // Check if content is null or empty
        const isEmpty = content === null || (
          content &&
          Object.values(content.questions).every(arr => arr.length === 0) &&
          Object.values(content.dares).every(arr => arr.length === 0)
        );

        if (isEmpty && navigationRef.current) {
          console.warn('Network disconnected with no valid content, showing NoInternetScreen with scenario: no-internet');
          // @ts-ignore - TS doesn't know about the navigate function on the ref
          navigationRef.current.navigate('NoInternetScreen', { scenario: 'no-internet' });
        } else if (content) {
          // Log content status
          const questionCount = Object.values(content.questions)
            .reduce((sum, questions) => sum + questions.length, 0);
          const dareCount = Object.values(content.dares)
            .reduce((sum, dares) => sum + dares.length, 0);

          console.log(`Network disconnected but using cached content: ${questionCount} questions, ${dareCount} dares`);
        }
      } else if (state.isConnected === true) {
        console.log('Network connection restored, will refresh content on next access');
      }
    });

    // Clean up listeners on unmount
    return () => {
      unsubscribe();
      appStateSubscription?.remove();
    };
  }, []);

  return (
    <AppSettingsProvider>
      <AdSettingsProvider>
        <AdBasedAccessProvider>
          <LanguageProvider>
            <GameProvider>
          <NavigationContainer theme={MyTheme} ref={navigationRef}>
            <StatusBar
              translucent={true}
              backgroundColor="transparent"
              barStyle="light-content"
            />
            <Stack.Navigator
              screenOptions={{
                headerShown: false,
                cardStyle: { backgroundColor: '#131416' },
                cardOverlayEnabled: Platform.OS === 'android',
                ...(Platform.OS === 'android' && {
                  cardStyleInterpolator: ({ current: { progress } }) => ({
                    cardStyle: {
                      opacity: progress.interpolate({
                        inputRange: [0, 0.5, 0.9, 1],
                        outputRange: [0, 0.25, 0.7, 1],
                      }),
                      backgroundColor: '#131416',
                    },
                    overlayStyle: {
                      opacity: progress.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, 0.5],
                        extrapolate: 'clamp',
                      }),
                      backgroundColor: '#131416',
                    }
                  })
                }),
                }}>
              <Stack.Screen name="Home" component={HomeScreen} />
              <Stack.Screen name="SelectCategories" component={SelectCategoriesScreen} />
              <Stack.Screen name="TouchGame" component={TouchGameScreen} />
              <Stack.Screen name="Settings" component={SettingsScreen} />
              <Stack.Screen name="WatchAdsScreen" component={WatchAdsScreen} />
              <Stack.Screen name="WhatsNewScreen" component={WhatsNewScreen} />
            </Stack.Navigator>
          </NavigationContainer>
            </GameProvider>
          </LanguageProvider>
        </AdBasedAccessProvider>
      </AdSettingsProvider>
    </AppSettingsProvider>
  );
});