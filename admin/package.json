{"name": "taptrap-admin", "version": "0.1.0", "private": true, "engines": {"node": ">=16.x"}, "dependencies": {"@chakra-ui/react": "^2.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "axios": "^1.9.0", "framer-motion": "^10.18.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-icons": "^4.12.0", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "recharts": "^2.15.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "vercel-build": "NODE_OPTIONS=\"--max-old-space-size=4096\" REACT_APP_API_URL=/api react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}