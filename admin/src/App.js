import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, Link as RouterLink } from 'react-router-dom';
import { Box, useToast, Button, VStack, Text, Center } from '@chakra-ui/react';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import ContentList from './pages/ContentList';
import EditContent from './pages/EditContent';
import AddContent from './pages/AddContent';
import GenerateContent from './pages/GenerateContent';
import PenaltyList from './pages/PenaltyList';
import EditPenalty from './pages/EditPenalty';
import AddPenalty from './pages/AddPenalty';
import AdSettings from './pages/AdSettings';
import PushNotifications from './pages/PushNotifications';
import WhatsNewManagement from './pages/WhatsNewManagement';
import PrivateRoute from './components/PrivateRoute';
import TestDashboard from './pages/TestDashboard';
import { useAuthContext } from './context/AuthContext';

function App() {
  const { isAuthenticated, loading } = useAuthContext();
  const navigate = useNavigate();
  const toast = useToast();

  useEffect(() => {
    console.log("App authentication state:", { isAuthenticated, loading });

    // Show authentication state in a toast for debugging
    if (!loading) {
      toast({
        title: isAuthenticated ? "Authenticated" : "Not authenticated",
        status: isAuthenticated ? "success" : "warning",
        duration: 2000,
        isClosable: true,
        position: "bottom-right"
      });
    }
  }, [isAuthenticated, loading, toast]);

  return (
    <Box>
      <Routes>
        <Route path="/login" element={<Login />} />

        {/* Remove bypass routes */}

        {/* Normal protected routes */}
        <Route path="/" element={<PrivateRoute><Dashboard /></PrivateRoute>} />
        <Route path="/content" element={<PrivateRoute><ContentList /></PrivateRoute>} />
        <Route path="/content/add" element={<PrivateRoute><AddContent /></PrivateRoute>} />
        <Route path="/content/generate" element={<PrivateRoute><GenerateContent /></PrivateRoute>} />
        <Route path="/content/edit/:id" element={<PrivateRoute><EditContent /></PrivateRoute>} />
        <Route path="/penalties" element={<PrivateRoute><PenaltyList /></PrivateRoute>} />
        <Route path="/penalties/add" element={<PrivateRoute><AddPenalty /></PrivateRoute>} />
        <Route path="/penalties/edit/:id" element={<PrivateRoute><EditPenalty /></PrivateRoute>} />
        <Route path="/ad-settings" element={<PrivateRoute><AdSettings /></PrivateRoute>} />
        <Route path="/push-notifications" element={<PrivateRoute><PushNotifications /></PrivateRoute>} />
        <Route path="/whatsnew" element={<PrivateRoute><WhatsNewManagement /></PrivateRoute>} />

        {/* Redirect to dashboard if route doesn't exist */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Box>
  );
}

export default App;