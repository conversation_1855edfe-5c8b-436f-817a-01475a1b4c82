require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./swagger');
const databaseService = require('./services/database.service');
const contentRoutes = require('./routes/content');
const penaltyRoutes = require('./routes/penalty');
const adminRoutes = require('./routes/admin');
const excludedContentRoutes = require('./routes/excludedContent');
const pushNotificationRoutes = require('./routes/pushNotifications');
const whatsNewRoutes = require('./routes/whatsNew');
const seedInitialContent = require('./utils/seedInitialContent');

// Initialize app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: function(origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if(!origin) return callback(null, true);
    // Allow all origins in development
    if(process.env.NODE_ENV === 'development') return callback(null, true);

    // Get allowed origins from environment variable or use default list
    let allowedOrigins = [
      'https://taptrap.app',
      'https://taptrap-backend.vercel.app',
      'https://taptrap-backend-git-feature-backend-eliezerpujols.vercel.app',
      'https://taptrap-admin.vercel.app',
      'https://taptrap-admin-git-feature-backend-eliezerpujols.vercel.app'
    ];

    // If CORS_ORIGINS is defined in .env, use it instead
    if (process.env.CORS_ORIGINS) {
      console.log('Using CORS origins from environment variable');
      allowedOrigins = process.env.CORS_ORIGINS.split(',').map(origin => origin.trim());
    }

    console.log('Allowed CORS origins:', allowedOrigins);

    // Check if the request origin is in the allowed list
    if(allowedOrigins.indexOf(origin) !== -1 || !origin) {
      callback(null, true);
    } else {
      // For Vercel deployment, both API and admin are served from the same domain
      // so we need to allow same-origin requests
      callback(null, true);
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'x-auth-token', 'Authorization'],
  credentials: true
}));
app.use(express.json());
app.use(morgan('dev'));

// Routes
app.use('/api/content', contentRoutes);
app.use('/api/penalties', penaltyRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/admin/excluded-content', excludedContentRoutes);
app.use('/api', pushNotificationRoutes);
app.use('/api/whatsnew', whatsNewRoutes);

// Swagger documentation
app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, { explorer: true }));

// Health check endpoints
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Root API endpoint
app.get('/api', (req, res) => {
  res.status(200).json({ message: 'TapTrap API is running' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'An unexpected error occurred',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// For Vercel serverless functions
module.exports = app;

// Create database connection
async function startServer() {
  try {
    const Content = require('./models/content');

    // Connect to the database using our database service
    await databaseService.connect();
    console.log('Database connection established');

    // Check if we need to seed the database
    if (process.env.NODE_ENV === 'development') {
      // Seed initial content for development
      await seedInitialContent();
    } else {
      // Only seed if database is empty in production
      const contentCount = await Content.countDocuments();
      if (contentCount === 0) {
        console.log('Database is empty. Seeding initial content...');
        await seedInitialContent();
      } else {
        console.log(`Database already has ${contentCount} content items`);
      }
    }

    // Only start server if not on Vercel (Vercel handles this for us)
    if (!process.env.VERCEL) {
      // Start server after database connection is established
      app.listen(PORT, () => {
        console.log(`Server running on port ${PORT}`);
      });
    }
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    if (!process.env.VERCEL) {
      process.exit(1);
    }
  }
}

startServer();