/**
 * Base Entity
 * Provides common functionality for all entity models
 */

const mongoose = require('mongoose');

/**
 * Create a base schema with common fields
 * @param {Object} definition - Schema definition
 * @param {Object} options - Schema options
 * @returns {mongoose.Schema} - Mongoose schema
 */
const createBaseSchema = (definition, options = {}) => {
  // Add common fields to all schemas
  const baseDefinition = {
    ...definition,
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  };

  // Create schema with provided options
  const schema = new mongoose.Schema(baseDefinition, options);

  // Add pre-save hook to update the updatedAt field
  schema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
  });

  return schema;
};

module.exports = {
  createBaseSchema
};
