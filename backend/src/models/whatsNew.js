const mongoose = require('mongoose');

/**
 * What's New Log Schema
 * Stores information about new features, updates, and announcements
 */
const whatsNewSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['dark', 'red', 'cyan', 'purple'],
    description: 'Visual theme type for the log card'
  },
  date: {
    type: String,
    required: true,
    description: 'Display date for the log (e.g., "JUN 13, 2023")'
  },
  title_en: {
    type: String,
    required: true,
    maxlength: 100,
    description: 'Title of the What\'s New log in English'
  },
  title_es: {
    type: String,
    required: true,
    maxlength: 100,
    description: 'Title of the What\'s New log in Spanish'
  },
  title_dom: {
    type: String,
    required: true,
    maxlength: 100,
    description: 'Title of the What\'s New log in Dominican Spanish'
  },
  description_en: {
    type: String,
    required: true,
    maxlength: 500,
    description: 'Description of the new feature or update in English'
  },
  description_es: {
    type: String,
    required: true,
    maxlength: 500,
    description: 'Description of the new feature or update in Spanish'
  },
  description_dom: {
    type: String,
    required: true,
    maxlength: 500,
    description: 'Description of the new feature or update in Dominican Spanish'
  },
  platform: {
    type: String,
    required: true,
    enum: ['ios', 'android', 'both'],
    default: 'both',
    description: 'Platform compatibility (iOS, Android, or Both)'
  },
  videoUrl: {
    type: String,
    required: false,
    description: 'Optional video URL for demonstration'
  },
  appVersion: {
    type: String,
    required: true,
    description: 'Minimum app version required for this feature (semver format)'
  }
}, {
  timestamps: true,
  collection: 'whatsNew'
});

// Add indexes for better query performance
whatsNewSchema.index({ createdAt: -1 });
whatsNewSchema.index({ appVersion: 1 });

// Virtual field to check if this log requires an app update
whatsNewSchema.virtual('requiresUpdate').get(function() {
  // This will be calculated on the client side based on current app version
  return false;
});

// Ensure virtual fields are serialized
whatsNewSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('WhatsNew', whatsNewSchema);
