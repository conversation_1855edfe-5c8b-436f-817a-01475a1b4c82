/**
 * Simple test script to verify What's New API endpoints
 * Run with: node test-whatsnew-api.js
 */

const axios = require('axios');

const API_BASE = 'http://localhost:5002/api';

// Test configuration
const testLog = {
  type: 'purple',
  date: 'DEC 15, 2024',
  title: 'TEST FEATURE',
  description: 'This is a test feature for API verification.',
  videoUrl: 'https://www.youtube.com/watch?v=test',
  appVersion: '1.0.15'
};

let authToken = '';
let createdLogId = '';

async function testAPI() {
  console.log('🧪 Testing What\'s New API endpoints...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health check:', healthResponse.data);

    // Test 2: Get public logs (should work without auth)
    console.log('\n2. Testing public logs endpoint...');
    try {
      const publicLogsResponse = await axios.get(`${API_BASE}/whatsnew`);
      console.log('✅ Public logs:', publicLogsResponse.data.length, 'logs found');
    } catch (error) {
      console.log('ℹ️  Public logs endpoint returned:', error.response?.status, error.response?.statusText);
    }

    // Test 3: Check new logs endpoint
    console.log('\n3. Testing check new logs endpoint...');
    try {
      const checkNewResponse = await axios.get(`${API_BASE}/whatsnew/check-new`);
      console.log('✅ Check new logs:', checkNewResponse.data);
    } catch (error) {
      console.log('ℹ️  Check new logs endpoint returned:', error.response?.status, error.response?.statusText);
    }

    // Test 4: Admin login (if credentials are available)
    console.log('\n4. Testing admin authentication...');
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || '30d9@5c1-24b2-4fb4';

    try {
      const loginResponse = await axios.post(`${API_BASE}/admin/login`, {
        email: adminEmail,
        password: adminPassword
      });
      authToken = loginResponse.data.token;
      console.log('✅ Admin login successful, token received');
    } catch (error) {
      console.log('❌ Admin login failed:', error.response?.data?.message || error.message);
      console.log('   Make sure backend is running and admin credentials are correct');
      return;
    }

    // Test 5: Create new log (admin only)
    console.log('\n5. Testing create log endpoint...');
    try {
      const createResponse = await axios.post(`${API_BASE}/whatsnew/admin`, testLog, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      createdLogId = createResponse.data.id;
      console.log('✅ Log created successfully:', createResponse.data.title);
    } catch (error) {
      console.log('❌ Create log failed:', error.response?.data?.message || error.message);
    }

    // Test 6: Get admin logs
    console.log('\n6. Testing admin logs endpoint...');
    try {
      const adminLogsResponse = await axios.get(`${API_BASE}/whatsnew/admin`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      console.log('✅ Admin logs:', adminLogsResponse.data.length, 'logs found');
    } catch (error) {
      console.log('❌ Admin logs failed:', error.response?.data?.message || error.message);
    }

    // Test 7: Update log (if we created one)
    if (createdLogId) {
      console.log('\n7. Testing update log endpoint...');
      try {
        const updateData = { ...testLog, title: 'UPDATED TEST FEATURE' };
        const updateResponse = await axios.put(`${API_BASE}/whatsnew/admin/${createdLogId}`, updateData, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        console.log('✅ Log updated successfully:', updateResponse.data.title);
      } catch (error) {
        console.log('❌ Update log failed:', error.response?.data?.message || error.message);
      }
    }

    // Test 8: Delete log (cleanup)
    if (createdLogId) {
      console.log('\n8. Testing delete log endpoint...');
      try {
        const deleteResponse = await axios.delete(`${API_BASE}/whatsnew/admin/${createdLogId}`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        console.log('✅ Log deleted successfully');
      } catch (error) {
        console.log('❌ Delete log failed:', error.response?.data?.message || error.message);
      }
    }

    console.log('\n🎉 API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the backend server is running on port 5002');
      console.log('   Run: cd backend && npm run dev');
    }
  }
}

// Run the tests
testAPI();
