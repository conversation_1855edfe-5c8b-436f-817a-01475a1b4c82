import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Platform, Modal, Animated as RNAnimated} from 'react-native';
import { typography } from '@/components/ThemedText';
import { StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';

// Libraries
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Text as SvgText } from 'react-native-svg';
import RevenueCatUI from 'react-native-purchases-ui';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing
} from 'react-native-reanimated';

// Services
import { useLanguage } from '@/services/i18nService';
import { useAppSettings } from '@/context/AppSettingsContext';
import { useAdBasedAccess } from '@/context/AdBasedAccessContext';
import { useAdSettings } from '@/context/AdSettingsContext';

// Effects
import * as Haptics from 'expo-haptics';
import { useSound } from '@/components/game/playSound';

// SVGs
import DialogTitleEnFirst from '@/assets/ads/ad_dialog_en_first.svg';
import DialogTitleEnSeq from '@/assets/ads/ad_dialog_en_seq.svg';
import DialogTitleEsFirst from '@/assets/ads/ad_dialog_es_first.svg';
import DialogTitleEsSeq from '@/assets/ads/ad_dialog_es_seq.svg';
import DialogTitleIcon from '@/assets/ads/icon_locked_shadow.svg'
import DialogCloseIcon from '@/assets/ads/icon_close_circle.svg';
import PlayWhiteIcon from '@/assets/ads/icon_play_white.svg';

// Dialog Props
interface Props {
  isSequential?: boolean;
  onClose?: () => void;
  isInsideModal?: boolean; // New prop to handle modal navigation issues
}

// Component
const unlockDialog: React.FC<Props> = ({ isSequential = false, onClose, isInsideModal = false }) => {
  const navigation = useNavigation();
  const { t, language } = useLanguage();
  const { playSound } = useSound();
  const { isHapticsOn, isSoundOn, unlockPremium } = useAppSettings();
  const { isInternetAvailable, checkInternetConnectivity } = useAdBasedAccess();
  const { isWatchAdsButtonEnabled } = useAdSettings();
  const [showPaywall, setShowPaywall] = useState(false);

  // Animation values
  const paywallFadeAnim = useRef(new RNAnimated.Value(0)).current;
  const overlayOpacity = useSharedValue(0);
  const dialogScale = useSharedValue(0);

  // Check internet connectivity when component mounts
  useEffect(() => {
    checkInternetConnectivity();
  }, []);

  // Trigger fade-in animation sequence when component mounts
  useEffect(() => {
    // 1. First fade in the overlay
    overlayOpacity.value = withTiming(1, {
      duration: 300,
      easing: Easing.inOut(Easing.ease)
    });

    // 2. After a small delay, show the dialog with scale animation
    setTimeout(() => {
      dialogScale.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.back(1.5))
      });
    }, 200); // 200ms delay like InGameBanner pattern
  }, []);

  // Animated style for the overlay fade-in effect
  const overlayAnimatedStyle = useAnimatedStyle(() => ({
    opacity: overlayOpacity.value,
  }));

  // Animated style for the dialog scale-in effect
  const dialogAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: dialogScale.value },
        { translateY: withTiming(dialogScale.value === 0 ? 50 : 0, { duration: 300 }) }
      ],
      opacity: dialogScale.value,
    };
  });

  // Handle paywall modal fade-in animation
  useEffect(() => {
    if (showPaywall) {
      paywallFadeAnim.setValue(0);
      setTimeout(() => {
        RNAnimated.timing(paywallFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }, 300); // delay before fade-in starts
    }
  }, [showPaywall]);

  // Function to get the correct dialog title SVG based on language and sequence
  const getDialogTitleSvg = () => {
    if (isSequential) {
      // For sequential dialogs
      switch (language) {
        case 'es':
          return <DialogTitleEsSeq style={styles.unlockHeaderText}/>;
        case 'dom':
          return <DialogTitleEsSeq style={styles.unlockHeaderText}/>; // Using Spanish for Dominican
        default:
          return <DialogTitleEnSeq style={styles.unlockHeaderText}/>;
      }
    } else {
      // For first-time dialogs
      switch (language) {
        case 'es':
          return <DialogTitleEsFirst style={styles.unlockHeaderText}/>;
        case 'dom':
          return <DialogTitleEsFirst style={styles.unlockHeaderText}/>; // Using Spanish for Dominican
        default:
          return <DialogTitleEnFirst style={styles.unlockHeaderText}/>;
      }
    }
  };

  const handleWatchAds = () => {
    if (isHapticsOn) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    if (isSoundOn) {
      playSound('tapEffect2');
    }

    // Check internet connectivity before navigating
    if (!isInternetAvailable) {
      console.log('UnlockDialog: No internet connection, cannot watch ads');
      return;
    }

    if (isInsideModal) {
      // Close the UnlockDialog first, then navigate
      // This is necessary because navigation from inside modals doesn't work properly on iOS
      if (onClose) {
        onClose();
      }

      // Use setTimeout to ensure the modal closes before navigation
      setTimeout(() => {
        navigation.navigate('WatchAdsScreen' as never);
      }, 100);
    } else {
      // Normal navigation flow when not inside a modal
      navigation.navigate('WatchAdsScreen' as never);
    }
  };

  const handleGetPremium = async () => {
    if (isHapticsOn) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    if (isSoundOn) {
      playSound('tapEffect2');
    }

    try {
      console.log(`UnlockDialog: Opening RevenueCat paywall on ${Platform.OS}`);

      if (Platform.OS === 'android') {
        // For Android, use the presentPaywall method
        try {
          console.log('UnlockDialog: Using RevenueCatUI.presentPaywall for Android');
          const result = await RevenueCatUI.presentPaywall({
            fontFamily: "Melindya",
          });
          console.log('UnlockDialog: Paywall result:', result);

          // Check if purchase was successful
          if (result === 'PURCHASED' || result === 'RESTORED') {
            console.log('UnlockDialog: Purchase completed via presentPaywall, updating premium status');
            unlockPremium();
            if (onClose) onClose();
          }
        } catch (paywallError) {
          console.error('UnlockDialog: Error presenting paywall on Android:', paywallError);
        }
      } else {
        // For iOS, use the Modal approach
        setShowPaywall(true);
      }
    } catch (error) {
      console.error('UnlockDialog: Error showing paywall:', error);
    }
  };

  const handleClose = () => {
    if (isHapticsOn) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    if (isSoundOn) {
      playSound('tapEffect2');
    }

    // Animate out the dialog first, then the overlay
    dialogScale.value = withTiming(0, {
      duration: 300,
      easing: Easing.inOut(Easing.ease)
    });

    // After dialog animation, fade out the overlay
    setTimeout(() => {
      overlayOpacity.value = withTiming(0, {
        duration: 300,
        easing: Easing.inOut(Easing.ease)
      });

      // After overlay fade-out, call onClose
      setTimeout(() => {
        if (onClose) {
          onClose();
        }
      }, 300);
    }, 300);
  };

  return (
    <Animated.View style={[styles.unlockOverlay, overlayAnimatedStyle]}>
      <LinearGradient
        colors={['#42E0FF', '#42FFB7']}
        style={styles.unlockOverlay}
      >
      <TouchableOpacity
        style={styles.unclockCloseButton}
        onPress={handleClose}
      >
        <DialogCloseIcon
          width={32}
          height={32}
        />
      </TouchableOpacity>

      {/* Container */}
      <Animated.View style={[styles.unlockContainer, dialogAnimatedStyle]} >
        {/* Header */}
        <View style={styles.unlockHeader}>
          <DialogTitleIcon
            style={styles.unlockHeaderIcon}
            width={100}
            height={100}
          />
          {getDialogTitleSvg()}
        </View>
        {/* Actions */}
        <View style={styles.unlockActions}>
          {/* Watch Ads Button - Only show if enabled in admin settings */}
          {isWatchAdsButtonEnabled && (
            <TouchableOpacity
              style={[
                styles.watchAdsButton,
                !isInternetAvailable && styles.watchAdsButtonDisabled
              ]}
              onPress={handleWatchAds}
              disabled={!isInternetAvailable}
            >
              <PlayWhiteIcon
                style={styles.watchAdsButtonIcon}
                width={32}
                height={32}
              />
              <Text style={[typography.subtitle2, styles.watchAdsButtonLabel]}>{t('watchAdsButton')}</Text>
            </TouchableOpacity>
          )}
          {/* Get Premium Button */}
          <TouchableOpacity
            style={styles.getPremiumButton}
            onPress={handleGetPremium}
          >
            <Svg height="60" width="200" style={styles.getPremiumButtonLabel}>
              {/* Outer stroke layer */}
              <SvgText
                x="50%"
                y="50%"
                fontSize="30"
                fontFamily="Melindya"
                fill="none"
                stroke="#FFFFFF"
                strokeWidth="4"
                textAnchor="middle"
                alignmentBaseline="middle"
              >
                {t('getPremium')}
              </SvgText>
              {/* Fill layer on top */}
              <SvgText
                x="50%"
                y="50%"
                fontSize="30"
                fontFamily="Melindya"
                fill="#000000"
                textAnchor="middle"
                alignmentBaseline="middle"
              >
                {t('getPremium')}
              </SvgText>
            </Svg>
            <LinearGradient
              colors={['#42E0FF', '#42FFB7']}
              style={styles.getPremiumButtonBg}
            />
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* RevenueCat Paywall Modal - iOS only */}
      {showPaywall && Platform.OS === 'ios' && (
        <Modal
          transparent
          visible
          animationType="none"
          onRequestClose={() => {
            console.log('UnlockDialog: Paywall modal closed by back button');
            setShowPaywall(false);
          }}
        >
          <RNAnimated.View style={{ flex: 1, opacity: paywallFadeAnim }}>
            <RevenueCatUI.Paywall
              options={{
                fontFamily: "Melindya",
                displayCloseButton: true,
              }}
              onDismiss={() => {
                console.log('UnlockDialog: Paywall dismissed');
                setShowPaywall(false);
              }}
              onPurchaseCompleted={() => {
                console.log('UnlockDialog: Purchase completed, updating premium status');
                unlockPremium();
                setShowPaywall(false);
                if (onClose) onClose();
              }}
              onPurchaseError={(error) => {
                console.error('UnlockDialog: Purchase error:', error);
                setShowPaywall(false);
              }}
            />
          </RNAnimated.View>
        </Modal>
      )}
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  unlockOverlay: {
    zIndex: 4,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unclockCloseButton: {
    zIndex: 3,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    right: 12,
    padding: 8,
  },
  unlockContainer: {
    width: 312,
    alignItems: 'center',
    borderRadius: 48,
    backgroundColor: '#F3EBDC',
    borderWidth: 6,
    borderStyle: 'solid',
    borderColor: '#131416',
    shadowColor: '#131416',
    shadowOffset: { width: 0, height: 4},
    shadowOpacity: 1,
    shadowRadius: 0,
    elevation: 0,
  },
  unlockHeader: {
    position: "relative",
    width: 312,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
    paddingBottom: 28,
  },
  unlockHeaderIcon: {
    zIndex: 3,
    position: 'absolute',
    top: -64,
    left: '50%',
    transform: [{ translateX: -48 }], // Half of the icon width (80/2 = 40)
  },
  unlockHeaderText: {
    zIndex: 2,
  },
  unlockActions: {
    position: 'relative',
    top: -2,
    gap: 14,
    width: 312,
    paddingHorizontal: 24,
    paddingBottom: 28,
  },
  watchAdsButton: {
    zIndex: 2,
    width: '100%',
    flexDirection: 'row',
    height: 64,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    borderRadius: 200,
    paddingLeft: 16,
    paddingRight: 32,
    backgroundColor: '#131416',
  },
  watchAdsButtonLabel: {
    top: 3,
    fontSize: 26,
    color: "#F3EBDC",
    lineHeight: 32,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  watchAdsButtonIcon: {
    left: 0,
  },
  watchAdsButtonDisabled: {
    opacity: 0.5,
    backgroundColor: '#666666',
  },
  getPremiumButton: {
    zIndex: 2,
    width: '100%',
    flexDirection: 'row',
    position: 'relative',
    height: 64,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    borderRadius: 200,
    backgroundColor: '#242529',
    borderWidth: 4,
    borderStyle: 'solid',
    borderColor: '#131416',
    shadowColor: '#131416',
    shadowOffset: { width: 0, height: 4},
    shadowOpacity: 1,
    shadowRadius: 0,
    elevation: 0,
  },
  getPremiumButtonLabel: {
    zIndex: 3,
    top: 3,
  },
  getPremiumButtonBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 200,
},
});

export default unlockDialog;
