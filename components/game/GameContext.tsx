import React, { createContext, useContext, useState, useEffect } from 'react';
import { fetchAndUpdateGameContent, getCachedGameContent } from '@/services/contentService';
import { useLanguage } from '@/services/i18nService';
import { useAppSettings } from '@/context/AppSettingsContext';

type ChallengeDifficulty = 'casual_dares' | 'mild_dares' | 'spicy_dares' | 'no_limits_dares' | 'couple_dares';
type QuestionCategory = 'casual_questions' | 'mild_questions' | 'spicy_questions' | 'no_limits_questions' | 'couple_questions';

interface Question {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}

interface Challenge {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}

interface GameContextType {
  selectedModes: ('questions' | 'challenges')[];
  selectedQuestionCategory: QuestionCategory;
  challengeDifficulty: ChallengeDifficulty;
  currentQuestion: Question | null;
  currentChallenge: Challenge | null;
  isLoading: boolean;
  selectedCategories: QuestionCategory[];
  setGameMode: (modes: ('questions' | 'challenges')[]) => void;
  setSelectedQuestionCategory: (category: QuestionCategory) => void;
  setChallengeDifficulty: (difficulty: ChallengeDifficulty) => void;
  setSelectedCategories: (categories: QuestionCategory[]) => void;
  getNextQuestion: () => Promise<Question | null>;
  getNextChallenge: () => Promise<Challenge | null>;
  resetGame: () => void;
  refreshContent: () => Promise<boolean>;
  getLocalizedTextForItem: (item: Question | Challenge | null) => string;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

export const GameProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedModes, setGameMode] = useState<('questions' | 'challenges')[]>([]);
  const [selectedQuestionCategory, setSelectedQuestionCategory] = useState<QuestionCategory>('casual_questions');
  const [challengeDifficulty, setChallengeDifficulty] = useState<ChallengeDifficulty>('casual_dares');
  const [gameContent, setGameContent] = useState<any>(null);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [currentChallenge, setCurrentChallenge] = useState<Challenge | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [usedQuestionIds, setUsedQuestionIds] = useState<Set<string>>(new Set());
  const [usedChallengeIds, setUsedChallengeIds] = useState<Set<string>>(new Set());
  const [selectedCategories, setSelectedCategories] = useState<QuestionCategory[]>([]);
  const { language } = useLanguage();
  const { isPremiumUnlocked } = useAppSettings();

  // Initialize game content from cache only - HomeScreen handles API fetching
  useEffect(() => {
    const loadCachedGameContent = async () => {
      try {
        // Always set isLoading to false after a short timeout to prevent UI blocking
        const timeoutId = setTimeout(() => {
          // If content hasn't loaded after 3 seconds, stop showing loading state
          setIsLoading(false);
        }, 3000);

        // Load only cached content - don't fetch from API
        // HomeScreen handles the comprehensive API calls including content fetching
        const cachedContent = await getCachedGameContent();

        if (cachedContent) {
          setGameContent(cachedContent);
          console.log('GameContext: Cached content loaded successfully');
        } else {
          // No cached content available - HomeScreen will handle fetching
          console.log('GameContext: No cached content available, waiting for HomeScreen to fetch');
        }

        clearTimeout(timeoutId);
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading cached game content in context:', error);
        setIsLoading(false);
      }
    };

    loadCachedGameContent();
  }, []);

  // Get a random question from the selected category
  const getNextQuestion = async (): Promise<Question | null> => {
    if (!gameContent || !gameContent.questions) {
      console.error('Game content not loaded yet');
      // Try to load cached content first - don't fetch from API
      try {
        const cachedContent = await getCachedGameContent();
        if (cachedContent) {
          setGameContent(cachedContent);
          console.log('GameContext: Loaded cached content for questions');
        } else {
          // No cached content available - display "Please connect to internet" message
          console.log('No cached content available for questions');
          return {
            id: 'no-internet',
            text_en: 'Please connect to the internet to play',
            text_es: 'Por favor conéctate a internet para jugar',
            text_dom: 'Por favor conéctate a internet pa\' jugal'
          };
        }
      } catch (error) {
        console.error('Failed to load cached game content on demand:', error);
        return {
          id: 'error',
          text_en: 'Error loading content. Please try again.',
          text_es: 'Error al cargar el contenido. Por favor, inténtalo de nuevo.',
          text_dom: 'Error al cargal el contenido. Inténtalo de nuevo.'
        };
      }
    }

    const premiumCategories: QuestionCategory[] = ['no_limits_questions', 'spicy_questions', 'couple_questions'];
    if (premiumCategories.includes(selectedQuestionCategory) && !isPremiumUnlocked) {
      console.warn(`Access to category "${selectedQuestionCategory}" is restricted. Premium required.`);
      return null;
    }

    const questions = gameContent.questions ? gameContent.questions[selectedQuestionCategory] : [];
    if (!questions || questions.length === 0) {
      console.error(`No questions found for category: ${selectedQuestionCategory}`);
      return null;
    }

    // Filter out questions that have already been used
    let availableQuestions = questions.filter((q: Question) => !usedQuestionIds.has(q.id));

    // If all questions have been used or no available questions, reset the pool and try again
    if (availableQuestions.length === 0) {
      console.log('All questions in this category have been used, resetting pool');
      setUsedQuestionIds(new Set());
      // After resetting, all questions should be available
      availableQuestions = [...questions];
    }

    // Select a random question
    const randomIndex = Math.floor(Math.random() * availableQuestions.length);
    const selectedQuestion = availableQuestions[randomIndex];

    // Mark this question as used
    setUsedQuestionIds(prev => new Set([...prev, selectedQuestion.id]));

    // Set as current question
    setCurrentQuestion(selectedQuestion);
    return selectedQuestion;
  };

  // Get a random challenge from the selected difficulty
  const getNextChallenge = async (): Promise<Challenge | null> => {
    if (!gameContent || !gameContent.dares) {
      console.error('Game content not loaded yet');
      // Try to load the content again by fetching from API
      try {
        const result = await fetchAndUpdateGameContent();
        if (result && result.content) {
          setGameContent(result.content);
        } else {
          // No content available - will display "Please connect to internet" message
          console.log('No content available for dares, network issue or empty response');
          return {
            id: 'no-internet',
            text_en: 'Please connect to the internet to play',
            text_es: 'Por favor conéctate a internet para jugar',
            text_dom: 'Por favor conéctate a internet pa\' jugal'
          };
        }
      } catch (error) {
        console.error('Failed to load game content on demand:', error);
        return {
          id: 'error',
          text_en: 'Error loading content. Please try again.',
          text_es: 'Error al cargar el contenido. Por favor, inténtalo de nuevo.',
          text_dom: 'Error al cargal el contenido. Inténtalo de nuevo.'
        };
      }
    }

    const challenges = gameContent.dares ? gameContent.dares[challengeDifficulty] : [];
    if (!challenges || challenges.length === 0) {
      console.error(`No challenges found for difficulty: ${challengeDifficulty}`);
      return null;
    }

    // Filter out challenges that have already been used
    let availableChallenges = challenges.filter((c: Challenge) => !usedChallengeIds.has(c.id));

    // If all challenges have been used or no available challenges, reset the pool and try again
    if (availableChallenges.length === 0) {
      console.log('All challenges in this difficulty have been used, resetting pool');
      setUsedChallengeIds(new Set());
      // After resetting, all challenges should be available
      availableChallenges = [...challenges];
    }

    // Select a random challenge
    const randomIndex = Math.floor(Math.random() * availableChallenges.length);
    const selectedChallenge = availableChallenges[randomIndex];

    // Mark this challenge as used
    setUsedChallengeIds(prev => new Set([...prev, selectedChallenge.id]));

    // Set as current challenge
    setCurrentChallenge(selectedChallenge);
    return selectedChallenge;
  };

  // Get localized text based on device language
  const getLocalizedTextForItem = (item: Question | Challenge | null): string => {
    if (!item) return '';
    if (language === 'es') return item.text_es;
    if (language === 'dom') return item.text_dom;
    return item.text_en; // Default to English
  };

  // Reset game state
  const resetGame = () => {
    setGameMode([]);
    setCurrentQuestion(null);
    setCurrentChallenge(null);
    setUsedQuestionIds(new Set());
    setUsedChallengeIds(new Set());
  };

  // Refresh content from API and update state
  const refreshContent = async (): Promise<boolean> => {
    try {
      console.log('GameContext: Refreshing content from API');
      setIsLoading(true);

      // Force fetch from API and update storage by passing true to forceRefresh
      const result = await fetchAndUpdateGameContent(true);

      if (result && result.content) {
        // Check if content is empty (no questions and no dares)
        const isEmpty = Object.values(result.content.questions).every(arr => arr.length === 0) &&
                        Object.values(result.content.dares).every(arr => arr.length === 0);

        if (!isEmpty) {
          // Update the game content state with the new content
          setGameContent(result.content);
          console.log('GameContext: Content refreshed successfully');
          setIsLoading(false);
          return true;
        } else {
          console.warn('GameContext: Content refresh returned empty content');
          setIsLoading(false);
          return false;
        }
      } else {
        console.warn('GameContext: Failed to refresh content');
        setIsLoading(false);
        return false;
      }
    } catch (error) {
      console.error('GameContext: Error refreshing content:', error);
      setIsLoading(false);
      return false;
    }
  };

  const value = {
    selectedModes,
    selectedQuestionCategory,
    challengeDifficulty,
    currentQuestion,
    currentChallenge,
    isLoading,
    selectedCategories,
    setGameMode,
    setSelectedQuestionCategory,
    setChallengeDifficulty,
    setSelectedCategories,
    getNextQuestion,
    getNextChallenge,
    resetGame,
    refreshContent,
    getLocalizedTextForItem
  };

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>;
};

export const useGameContext = () => {
  const context = useContext(GameContext);
  if (context === undefined) {
    throw new Error('useGameContext must be used within a GameProvider');
  }
  return context;
};
