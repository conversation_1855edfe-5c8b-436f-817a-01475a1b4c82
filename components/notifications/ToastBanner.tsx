import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle, ActivityIndicator } from 'react-native';
import { typography } from '@/components/ThemedText';

// Services
import { useLanguage } from '@/services/i18nService';

// SVGs
import ErrorIcon from '@/assets/notifications/icon_error.svg';
import SuccessIcon from '@/assets/notifications/icon_success.svg';
import CloseIcon from '@/assets/notifications/icon_close.svg';

type ToastState = 'error' | 'success' | 'loading';

interface Props {
  onPress?: () => void;
  onClose?: () => void;
  showCloseButton?: boolean;
  style?: ViewStyle;
  state: ToastState;
}

const toastBanner: React.FC<Props> = ({ onPress, onClose, showCloseButton = false, style, state }) => {
  const { t } = useLanguage();

  console.log('🍞 ToastBanner props:', { state, showCloseButton, hasOnPress: !!onPress, hasOnClose: !!onClose });

  // Get dynamic content based on state
  const getTitle = () => {
    switch (state) {
      case 'error':
        return t('toastErrorTitle');
      case 'success':
        return t('toastSuccessTitle');
      case 'loading':
        return t('toastLoadingTitle');
      default:
        return t('toastErrorTitle');
    }
  };

  const getLabel = () => {
    switch (state) {
      case 'success':
        return t('toastSuccessLabel');
      case 'loading':
        return t('toastLoadingLabel');
      case 'error':
        return t('toastErrorLabel');
      default:
        return '';
    }
  };

  const getStateStyle = () => {
    switch (state) {
      case 'error':
        return styles.toastBannerError;
      case 'success':
        return styles.toastBannerSuccess;
      case 'loading':
        return styles.toastBannerLoading;
      default:
        return styles.toastBannerError;
    }
  };

  const renderIcon = () => {
    switch (state) {
      case 'error':
        return <ErrorIcon style={styles.toastBannerIcon} width={28} height={28} />;
      case 'success':
        return <SuccessIcon style={styles.toastBannerIcon} width={28} height={28} />;
      case 'loading':
        return (
          <ActivityIndicator
            size="large"
            color="#131416"
            style={styles.toastBannerIcon}
          />
        );
      default:
        return <ErrorIcon style={styles.toastBannerIcon} width={28} height={28} />;
    }
  };

  return (
    <View style={[styles.toastBanner, getStateStyle(), style]}>
      {renderIcon()}
      <View style={styles.toastBannerContent}>
        <Text style={[typography.nunitoBold, styles.toastBannerTitle]}>{getTitle()}</Text>

        {/* Show label for success and loading states */}
        {(state === 'success' || state === 'loading' || state === 'error') && (
          <Text style={[typography.nunitoSemiBold, styles.toastBannerLabel]}>{getLabel()}</Text>
        )}

        {/* Show retry button for error state when onPress is provided */}
        {state === 'error' && onPress && (
          <TouchableOpacity style={[styles.toastBannerButton]} onPress={onPress}>
            <Text style={[typography.subtitle2, styles.toastBannerButtonText]}>
              {t('toastErrorButton')}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Close button - positioned in top-right corner */}
      {showCloseButton && (
        <TouchableOpacity
          style={styles.closeButton}
          onPress={onClose}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <CloseIcon width={32} height={32} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  toastBanner: {
    width: 290,
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
  },
  toastBannerLoading: {
    borderLeftWidth: 4,
    borderLeftColor: '#42E8EA',
  },
  toastBannerError: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF4274',
  },
  toastBannerSuccess: {
    borderLeftWidth: 4,
    borderLeftColor: '#42FFB7',
  },
  toastBannerContent: {
    gap: 4,
    flex: 1,
  },
  toastBannerTitle: {
    color: '#131416',
    fontSize: 18,
    // letterSpacing: 1.2,
  },
  toastBannerButton: {
    marginTop: 4,
  },
  toastBannerLabel: {
    color: '#5E5E69',
    fontSize: 16,
    // letterSpacing: 1.2,
  },
  toastBannerButtonText: {
    textTransform: 'uppercase',
    fontSize: 22,
    lineHeight: 32,
    color: '#FF4589',
    letterSpacing: 1.2,
  },
  toastBannerIcon: {
    marginRight: 12,
  },
  closeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    zIndex: 1,
  },
});

export default toastBanner;