# TapTrap Documentation

## Overview

TapTrap is a React Native mobile game where players use multi-touch input to engage in a selection process. The game supports multiple fingers on the screen and randomly selects one after a countdown. The selected finger is highlighted, and the player is presented with a question or challenge to complete.

## Table of Contents

1. [Project Structure](#project-structure)
2. [Core Features](#core-features)
3. [Game Mechanics](#game-mechanics)
4. [Backend Integration](#backend-integration)
5. [Localization](#localization)
6. [Installation and Setup](#installation-and-setup)
7. [Development Guide](#development-guide)
8. [Troubleshooting](#troubleshooting)

## Project Structure

The TapTrap project follows a standard React Native application structure:

```
TapTrap/
├── admin/                  # Admin dashboard for content management
├── assets/                 # Images, sounds, and other static resources
├── components/             # Reusable UI components
├── context/                # React context providers
├── docs/                   # Project documentation
├── node_modules/           # Dependencies
├── screens/                # Main application screens
├── services/               # API and utility services
├── App.tsx                 # Main application component
├── package.json            # Project dependencies and scripts
└── README.md               # Project overview
```

## Core Features

- **Multi-touch Game**: Supports multiple fingers on the screen simultaneously
- **Random Selection**: Selects one finger after a 3-second countdown
- **Questions and Challenges**: Presents questions or challenges based on selected categories
- **Multilingual Support**: Available in English, Spanish, and Dominican Spanish
- **Content Management**: Backend integration for dynamic content updates
- **Offline Support**: Works offline with cached content
- **Sound Effects**: Interactive audio feedback
- **Haptic Feedback**: Tactile response on supported devices

## Game Mechanics

### Touch Detection

The game uses React Native's touch event system to track multiple fingers on the screen. Each touch point is assigned a unique identifier and tracked in the application state.

### Selection Process

1. Players place at least two fingers on the screen
2. A 3-second countdown begins
3. After the countdown, one finger is randomly selected
4. The selected finger is highlighted
5. A question or challenge is presented to the player

### Content Categories

The game offers different categories of content:

**Questions:**
- Casual Questions
- Mild Questions
- Spicy Questions
- No Limits Questions

**Challenges:**
- Casual Dares
- Mild Dares
- Spicy Dares
- No Limits Dares

## Backend Integration

TapTrap integrates with a custom backend that replaces the previous Airtable implementation.

### API Endpoints

- `GET /api/content` - Returns all game content formatted for the app
- `GET /api/content/:gameMode/:category` - Returns specific content by game mode and category

### Content Structure

Content in the backend follows this schema:

```json
{
  "id": "string",            // Unique identifier
  "text_en": "string",       // English text
  "text_es": "string",       // Spanish text
  "text_dom": "string",      // Dominican Spanish text
  "category": "string",      // e.g., "casual_questions", "mild_dares", etc.
  "gameMode": "string",      // "questions" or "dares"
  "active": "boolean",       // Whether to include in app content
  "createdAt": "date",       // Creation timestamp
  "updatedAt": "date"        // Last update timestamp
}
```

### Content Caching

The app implements a robust caching system:

1. Attempts to fetch content from the backend API
2. Stores fetched content in the device's file system
3. Falls back to cached content when offline
4. Uses bundled content as a last resort

## Localization

TapTrap supports multiple languages through the i18nService:

- English (default)
- Spanish
- Dominican Spanish

The app automatically detects the device language and displays content accordingly. All game content (questions and challenges) is stored in all three languages. The app will automatically use Dominican Spanish for users with Spanish language and Dominican Republic region code.

## Installation and Setup

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- React Native development environment
- Expo CLI

### Installation Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/TapTrap.git
   cd TapTrap
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Start the development server:
   ```bash
   expo start
   # or
   npm start
   ```

4. Run on a device or emulator:
   - Press 'a' for Android
   - Press 'i' for iOS
   - Scan the QR code with the Expo Go app

## Development Guide

### Adding New Features

1. Create a new branch for your feature:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Implement your changes
3. Test thoroughly on both Android and iOS
4. Submit a pull request

### Modifying Game Content

Game content can be modified through:

1. The admin dashboard (preferred method)
2. Directly editing the backend database
3. Updating the bundled content in `assets/game_content.json`

### Building for Production

To create a production build:

```bash
expo build:android  # For Android
expo build:ios      # For iOS
```

## Troubleshooting

### Common Issues

**No content appears in the game:**
- Check internet connection
- Verify backend API is accessible
- Check if cached content exists

**Game crashes when selecting a finger:**
- Ensure at least two fingers are on the screen
- Check for errors in the console logs
- Verify content structure is correct

**Backend connection issues:**
- Confirm API_URL in contentService.ts is correct
- Check for CORS issues
- Verify backend server is running

For more detailed troubleshooting, refer to the admin/TROUBLESHOOTING.md file.
