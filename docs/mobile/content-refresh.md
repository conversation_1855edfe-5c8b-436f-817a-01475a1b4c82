# TapTrap Content Refresh Mechanism

## Overview

The TapTrap app implements a content refresh mechanism that ensures users always have access to the latest content from the backend API. This document explains how content is refreshed and how text updates are applied to existing content pools.

## Content Refresh Flow

1. **Refresh Triggers**:
   - When the app starts (after assets are loaded)
   - ~~When the Home screen receives focus~~ (Removed in optimization)
   - A debounce mechanism prevents duplicate refreshes within 10 seconds

2. **Refresh Process**:
   - The app fetches fresh content from the API
   - The fresh content is saved to encrypted AsyncStorage
   - Text fields in existing content pools are immediately updated
   - Detailed logs show what content was added, removed, or updated

3. **Pool Impact**:
   - New content is immediately added to all existing pools
   - Inactive or removed content is immediately removed from all existing pools
   - Text updates are immediately applied to all existing pools
   - All changes preserve the used/unused status of existing items

## Error Handling and Fallbacks

The app implements robust error handling to ensure a good user experience even when content refresh fails:

1. **API Error Handling**:
   - When API requests fail (404, network errors, etc.), the app attempts to use cached content
   - If no cached content is available, the app shows the NoInternetScreen
   - Detailed error logs help identify the specific cause of failures

2. **Empty Content Detection**:
   - The app detects when content is structurally valid but empty (no questions or dares)
   - Empty content is treated as an error condition, showing the NoInternetScreen
   - This prevents the app from appearing to work but showing no content to users

3. **NoInternetScreen Behavior**:
   - The NoInternetScreen is shown when:
     - There is no internet connection AND no cached content
     - There is internet connection BUT the API request failed
     - The API returned empty content (no questions or dares)
   - The retry button attempts to refresh content from the API
   - The screen remains visible until valid content is successfully loaded
   - Visual feedback is provided when retry attempts fail
   - A loading spinner is displayed during content refresh attempts
   - Navigation only occurs after content is successfully loaded

4. **Fallback Content Structure**:
   - A minimal valid content structure is provided as a last resort
   - This prevents crashes from null references in the app's components
   - The empty structure is only used internally and triggers appropriate error screens

```typescript
// Example of empty content structure used as fallback
const createEmptyGameContent = (): GameContent => ({
  questions: {
    casual_questions: [],
    mild_questions: [],
    spicy_questions: [],
    no_limits_questions: []
  },
  dares: {
    casual_dares: [],
    mild_dares: [],
    spicy_dares: [],
    no_limits_dares: []
  }
});
```

## Pool Management Implementation

The app includes a comprehensive pool management mechanism that ensures pools are always up-to-date with the latest content from the API. This ensures that users immediately see updated content without needing to wait for pool resets.

### Key Components

1. **`updatePoolItemsText` Function**:
   - Located in `poolStorageService.ts`
   - Called after fresh content is fetched in `HomeScreen.tsx`
   - Updates all stored pools with the new content

2. **Update Process**:
   - Retrieves all stored pools from AsyncStorage
   - Creates lookup maps of fresh content items by ID (excluding inactive items)
   - For each pool:
     - Updates text fields of existing items
     - Removes items that no longer exist in the API response or are marked as inactive
     - Adds new items that weren't previously in the pool
     - Preserves the used/unused status of existing items
   - Saves the updated pools back to AsyncStorage
   - Logs detailed statistics about updates, additions, and removals

### Code Example

```typescript
// Update text in existing pools with the fresh content
await updatePoolItemsText(freshContent);
```

## Benefits

1. **Immediate Text Updates**: Users see updated content text immediately, without waiting for pool resets
2. **Efficient Updates**: Only text fields are updated, preserving other item properties
3. **Comprehensive Coverage**: All stored pools are updated, regardless of current game mode
4. **Detailed Logging**: The app logs detailed statistics about the updates for debugging

## Technical Details

### Pool Update Logic

The pool update process follows these steps for each stored pool:

1. **Filter out inactive content**: When creating lookup maps, items marked as "Inactive" are excluded
2. **Respect category boundaries**: Only content from categories that match the pool's categories is considered
3. **Update existing items**: Text fields are updated while preserving other properties
4. **Remove non-existent items**: Items that no longer exist in the API response are removed
5. **Add new items**: New items from the API that weren't in the pool are added (only for matching categories)
6. **Preserve item state**: The used/unused status of existing items is maintained

```typescript
// Extract categories from the pool key
// The key format is: taptrap_question_pool_category1_category2_...
const keyParts = poolKey.replace(POOL_STORAGE_KEY_PREFIX, '').split('_');
const poolCategories = keyParts.filter(part => part !== 'all' && part !== '');

// Step 1: Update existing items and mark items for removal
const updatedPool = [];
const existingIds = new Set();

for (let i = 0; i < pool.length; i++) {
  const item = pool[i];

  // Find the fresh item in the appropriate category map
  let freshItem = null;
  if (item.type === 'question' && freshContentMaps.questions[item.category]) {
    freshItem = freshContentMaps.questions[item.category].get(item.id);
  } else if (item.type === 'challenge' && freshContentMaps.dares[item.category]) {
    freshItem = freshContentMaps.dares[item.category].get(item.id);
  }

  // If the item exists in fresh content, update it and keep it
  if (freshItem) {
    existingIds.add(item.id);

    // Check if any text fields have changed
    if (
      item.text_en !== freshItem.text_en ||
      item.text_es !== freshItem.text_es ||
      item.text_dom !== freshItem.text_dom
    ) {
      // Update the text fields but preserve other properties
      updatedPool.push({
        ...item,
        text_en: freshItem.text_en,
        text_es: freshItem.text_es,
        text_dom: freshItem.text_dom
      });
      poolUpdated = true;
      itemsUpdatedInPool++;
    } else {
      // No changes needed, keep the item as is
      updatedPool.push(item);
    }
  } else {
    // Item no longer exists in fresh content or is inactive, remove it
    poolUpdated = true;
    itemsRemovedFromPool++;
    // Don't add to updatedPool
  }
}

// Step 2: Add new items that weren't in the pool before, but only for matching categories
const shouldIncludeCategory = (category: string): boolean => {
  // If poolCategories is empty or contains 'all', include all categories
  if (poolCategories.length === 0 || poolCategories.includes('all')) {
    return true;
  }
  // Otherwise, only include if the category is in poolCategories
  return poolCategories.includes(category);
};

// Add new questions for matching categories
Object.keys(freshContentMaps.questions).forEach(category => {
  if (shouldIncludeCategory(category)) {
    freshContentMaps.questions[category].forEach((item, id) => {
      if (!existingIds.has(id)) {
        newItems.push({
          id: item.id,
          text_en: item.text_en,
          text_es: item.text_es,
          text_dom: item.text_dom,
          type: 'question',
          category: item.category
        });
        itemsAddedToPool++;
      }
    });
  }
});
```

### Performance Considerations

- The update process uses Map objects for O(1) lookups of fresh content items
- Only pools with actual changes are saved back to AsyncStorage
- The process continues even if one pool fails to update
- Detailed logs help identify any issues with the update process
- Inactive content is filtered out early to improve efficiency

## Performance Optimizations

### Time-Based Throttling for Content Refresh

To reduce unnecessary API calls and improve performance, a time-based throttling mechanism has been implemented for content refreshes. This ensures that the app doesn't make too many API requests in a short period of time.

#### Implementation Details

1. **Timestamp Tracking**:
   - The app stores the timestamp of the last successful content refresh in AsyncStorage
   - Before making a new API request, it checks if at least 20 minutes have passed since the last refresh
   - If less than 20 minutes have passed and `forceRefresh` is false, it returns the cached content without making a new request

2. **Force Refresh Option**:
   - The `forceRefresh` parameter can be used to bypass the time check and always fetch fresh content
   - This is useful for scenarios where fresh content is critical, such as after a user purchases premium access

3. **Empty Cache Handling**:
   - If no cached content is found or the cached content is empty (contains no questions or dares), the app forces a refresh regardless of the time threshold
   - This ensures users always have content to interact with, even if the time threshold hasn't been reached

```typescript
// Check if we've refreshed content recently
const now = Date.now();
const lastRefreshTime = await getLastContentRefreshTime();
const timeSinceLastRefresh = now - lastRefreshTime;

if (timeSinceLastRefresh < 3600000) { // 60 minutes threshold
  console.log('⏱️ CONTENT: Skipping content refresh (last refresh was',
             Math.round(timeSinceLastRefresh / 1000), 'seconds ago)');

  // Return cached content without making a new API request
  const cachedContent = await getCachedGameContent();
  if (cachedContent) {
    const totalQuestions = Object.values(cachedContent.questions)
      .reduce((sum, arr) => sum + arr.length, 0);
    const totalDares = Object.values(cachedContent.dares)
      .reduce((sum, arr) => sum + arr.length, 0);

    // Si hay contenido en el caché, lo usamos
    if (totalQuestions > 0 || totalDares > 0) {
      console.log(`📦 Using cached content: ${totalQuestions} questions, ${totalDares} dares`);
      return cachedContent;
    } else {
      // Si el caché está vacío (sin preguntas ni retos), forzamos un refresh
      console.log('⚠️ Cached content exists but is empty, forcing refresh');
      forceRefresh = true;
    }
  } else {
    // Si no hay contenido en caché, forzamos un refresh
    console.log('⚠️ No cached content available, forcing refresh');
    forceRefresh = true;
  }
}
```

4. **Timestamp Update After Successful Refresh**:
   - After successfully fetching and saving content, the app updates the timestamp in AsyncStorage
   - This ensures the time threshold works correctly for future refresh attempts

```typescript
// Update the last refresh timestamp
const timestampUpdated = await updateLastContentRefreshTime();
if (timestampUpdated) {
  console.log('✅ Last content refresh timestamp updated successfully');
} else {
  console.warn('⚠️ Failed to update last content refresh timestamp');
}
```

#### Benefits

1. **Reduced API Calls**: The time threshold prevents excessive API calls in a short period
2. **Improved Performance**: Less network activity and processing leads to better overall app performance
3. **Battery Efficiency**: Fewer network requests means less battery consumption
4. **Content Availability**: Forcing refresh when cache is empty ensures users always have content
5. **Detailed Logging**: Emoji indicators in logs make it easy to track the refresh process

### App Launch Only Content Refresh

To further improve performance, especially on Android devices, the content refresh mechanism was optimized to only fetch content when the app launches, not every time the user returns to the home screen.

#### Implementation Details

1. **First Mount Tracking**:
   - A `isFirstMount` ref tracks whether this is the first time the component is mounting
   - Content is only refreshed on the first mount (app launch)
   - Subsequent navigations to the home screen will not trigger content refreshes

```typescript
// Add refs to track the last content refresh time and first mount status
const lastRefreshTimeRef = useRef<number>(0);
const isFirstMount = useRef<boolean>(true);

// Effect to load assets and refresh content only when the component mounts (app launch)
useEffect(() => {
  async function loadAssets() {
    try {
      // ... asset loading code ...

      // Only refresh game content on first mount (app launch)
      // This prevents unnecessary API calls when navigating back to HomeScreen
      if (isFirstMount.current) {
        console.log('HOME: Initial app launch, refreshing content...');
        refreshGameContent();
        isFirstMount.current = false;
      } else {
        console.log('HOME: Returning to HomeScreen, skipping content refresh');
      }
    } catch (error) {
      console.error('Error loading assets:', error);
    }
  }

  loadAssets();
}, []);
```

2. **Removed Focus Event Listener**:
   - The focus event listener that was triggering content refresh on every navigation to the home screen was removed
   - This significantly reduces unnecessary API calls and improves performance

3. **Unified Content Refresh Approach**:
   - The app now uses the same content refresh approach for both iOS and Android platforms
   - Platform-specific optimizations have been removed to simplify the codebase
   - The content refresh mechanism relies on the time-based throttling system to prevent excessive API calls
   - This unified approach ensures consistent behavior across all platforms

```typescript
// Refresh game content using the standard approach
const freshContent = await fetchAndUpdateGameContent();
```

#### Benefits

1. **Reduced API Calls**: By only refreshing content on app launch, the number of API calls is significantly reduced
2. **Improved Performance**: Less network activity and processing leads to better overall app performance
3. **Battery Efficiency**: Fewer network requests means less battery consumption
4. **Faster Navigation**: Returning to the home screen is faster since it doesn't trigger content refreshes
5. **Cross-Platform Consistency**: Unified approach ensures consistent behavior across iOS and Android

## Offline Functionality

TapTrap is designed to work seamlessly both online and offline. The app implements robust mechanisms to ensure users can continue playing even without an internet connection.

### Offline Content Loading Flow

1. **Network Detection**: The app uses `NetInfo` to detect network connectivity status
2. **Cached Content Access**: When offline, the app attempts to load content from encrypted AsyncStorage
3. **Empty Cache Handling**: If cached content exists but is empty, the app still uses it when offline to prevent showing the NoInternetScreen
4. **Fallback Mechanisms**: Multiple fallback strategies ensure the app can function even with corrupted cache
5. **Minimal UI Disruption**: The app only shows the NoInternetScreen as an absolute last resort

```typescript
// When offline with empty cache
if (totalQuestions > 0 || totalDares > 0) {
  console.log(`📦 Using cached content while offline: ${totalQuestions} questions, ${totalDares} dares`);
  return cachedContent;
} else {
  console.warn('⚠️ Cached content exists but contains no items while offline');
  // Cuando estamos offline y el caché está vacío, aún devolvemos la estructura vacía
  // para evitar mostrar la pantalla NoInternetScreen
  return cachedContent;
}
```

### Robust Cache Handling

The app implements several strategies to handle cached content, especially when offline:

1. **Network-Aware Error Handling**:
   - **When Online**: Corrupted cache is deleted to make room for fresh content
   - **When Offline**: Corrupted cache is preserved for potential recovery and fallback mechanisms are used

2. **Multiple Decryption Strategies**:
   - Primary decryption using CryptoJS
   - Alternative decoding methods if primary fails
   - Attempts to salvage partial data from corrupted cache
   - Support for different storage format versions

3. **Content Structure Fallbacks**:
   - Empty but valid content structures are provided when no cache is available
   - Partial content is salvaged when possible
   - The app can function with minimal content rather than showing an error screen

### Implementation Details

#### 1. Resilient Decryption

```typescript
// Function to decrypt data from local storage with multiple fallback mechanisms
export const decryptFromStorage = (storedData: string): any => {
  try {
    // Check the version prefix
    if (storedData.startsWith(`${STORAGE_VERSION_KEY}:`)) {
      // Extract the base64 encoded data
      const base64Data = storedData.substring(STORAGE_VERSION_KEY.length + 1);

      try {
        // Primary decryption method
        const wordArray = CryptoJS.enc.Base64.parse(base64Data);
        const decodedString = CryptoJS.enc.Utf8.stringify(wordArray);

        try {
          // Parse the JSON
          const parsedData = JSON.parse(decodedString);
          return parsedData;
        } catch (parseError) {
          // Try to salvage partial data if JSON parsing fails
          // ...salvage logic...
        }
      } catch (decodeError) {
        // Try alternative decoding method as fallback
        try {
          // Simple base64 decode as fallback
          const fallbackDecoded = atob(base64Data);
          // ...fallback parsing logic...
        } catch (fallbackError) {
          // ...additional fallbacks...
        }
      }
    } else {
      // Unknown format - try to handle legacy formats or corrupted data
      // ...recovery attempts...
    }
  } catch (error) {
    // ...error handling...
  }
};
```

#### 2. Network-Aware Cache Handling

```typescript
// Function to get cached game content with improved offline handling
export const getCachedGameContent = async (): Promise<GameContent | null> => {
  // Check network status to determine how to handle errors
  const networkState = await NetInfo.fetch();
  const isOnline = networkState.isConnected === true;

  try {
    // ...content retrieval logic...
  } catch (readError) {
    // Only delete corrupted cache when online
    if (isOnline) {
      await AsyncStorage.removeItem(GAME_CONTENT_STORAGE_KEY);
      return null; // Will trigger a fresh fetch
    } else {
      // When offline, provide an empty but valid structure as fallback
      return createEmptyGameContent();
    }
  }
};
```

#### 3. Empty Content Fallback

```typescript
// Default minimal game content structure for fallback
const createEmptyGameContent = (): GameContent => ({
  questions: {
    casual_questions: [],
    mild_questions: [],
    spicy_questions: [],
    no_limits_questions: []
  },
  dares: {
    casual_dares: [],
    mild_dares: [],
    spicy_dares: [],
    no_limits_dares: []
  }
});
```

### Benefits

1. **Uninterrupted Gameplay**: Users can continue playing even without internet connection
2. **Data Preservation**: Cached content is preserved when offline to ensure availability
3. **Graceful Degradation**: The app provides the best possible experience based on available data
4. **Resilient Storage**: Multiple strategies for handling corrupted cache data
5. **Minimal Disruption**: The NoInternetScreen is only shown as an absolute last resort

## Implemented Improvements

Recent enhancements to the content refresh and offline mechanisms:

1. **Time-Based Throttling**: Implemented a 20-minute threshold to limit API requests and improve performance
2. **Empty Cache Handling**: Added logic to force content refresh when cache is empty, ensuring users always have content
3. **App Launch Only Refresh**: Optimized to only fetch content when the app launches, not on every navigation to home screen
4. **Detailed Logging**: Added emoji indicators to logs for better visibility and debugging
5. **Offline Empty Cache Handling**: Improved handling of empty cache in offline mode to prevent showing NoInternetScreen
6. **Unified Platform Approach**: Removed platform-specific content refresh logic to ensure consistent behavior across iOS and Android
7. **Enhanced NoInternetScreen UX**: Improved user experience with loading indicators and proper navigation flow
8. **Safe Navigation Pattern**: Implemented a more robust navigation approach using CommonActions.reset instead of StackActions.pop
9. **Visual Loading Feedback**: Added loading spinner to the retry button to indicate content refresh in progress

### Enhanced NoInternetScreen Implementation

The NoInternetScreen has been improved to provide better user experience and more reliable navigation:

1. **Loading State Management**:
   - Added a loading state to track when content refresh is in progress
   - Prevents multiple simultaneous refresh attempts
   - Provides visual feedback to users during the refresh process

2. **Visual Loading Indicator**:
   - Replaced the retry button text with a spinner during content refresh
   - The button is disabled during loading to prevent multiple taps
   - Provides clear visual feedback that the app is working

3. **Improved Navigation Flow**:
   - Navigation only occurs after content is successfully loaded
   - Uses CommonActions.reset to ensure a clean navigation state
   - Prevents black screen issues that could occur with previous implementation

```typescript
// Example of improved retry handler with loading state
const handleRetry = async () => {
  // Prevent multiple attempts if already loading
  if (isLoading) return;

  // Activate loading state
  setIsLoading(true);

  const networkState = await NetInfo.fetch();

  if (networkState.isConnected) {
    try {
      const refreshSuccessful = await refreshContent();

      if (refreshSuccessful) {
        // Navigate only after successful content refresh
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: 'Home' }],
          })
        );
      } else {
        // Deactivate loading state if refresh failed
        setIsLoading(false);
        // Show visual feedback
        animateButtonForFeedback();
      }
    } catch (error) {
      setIsLoading(false);
      animateButtonForFeedback();
    }
  } else {
    // Still offline
    setIsLoading(false);
    animateButtonForFeedback();
  }
};
```

4. **Benefits**:
   - Improved user experience with clear loading indicators
   - More reliable navigation prevents black screen issues
   - Proper error handling with visual feedback
   - Prevents multiple simultaneous refresh attempts

## Future Improvements

Potential enhancements to the content refresh and offline mechanisms:

1. **Selective Pool Updates**: Update only pools that are likely to be used soon
2. **Background Processing**: Move the update process to a background thread
3. **Differential Updates**: Only fetch and apply changes since the last update
4. **User Control**: Add a manual refresh button for users to force content updates
5. **Scheduled Refreshes**: Implement periodic background refreshes at strategic times
6. **Partial Content Updates**: Only fetch and update changed content to reduce data usage
7. **Cache Versioning**: Better handling of cache format changes between app versions
8. **Content Prioritization**: Cache most important content first for limited storage scenarios
9. **Background Sync**: Update content when the app is in the background and connection is restored
