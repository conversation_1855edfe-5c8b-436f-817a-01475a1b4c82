import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  FC,
} from 'react';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Type for language
type LanguageType = 'en' | 'es' | 'dom';

// Translation dictionary
const translations = {
  pickTheMode: {
    en: 'Choose a Game Mode',
    es: '¿Qué vamos a jugar?',
    dom: '¿Qué vamos a jugar?'
  },
  questionsIndex: {
    en: 'Questions',
    es: 'Preguntas',
    dom: 'Preguntas'
  },
  challengesIndex: {
    en: 'Dares',
    es: 'Retos',
    dom: 'Retos'
  },
  pickTheCategory: {
    en: 'Pick the category',
    es: 'Elige la categoría',
    dom: 'Elige la categoría'
  },
  casual: {
    en: 'Casual',
    es: 'Casual',
    dom: 'Casual'
  },
  friends: {
    en: 'Daring',
    es: 'Atrevido',
    dom: 'Atrevido'
  },
  partners: {
    en: 'Spicy',
    es: 'Pican<PERSON>',
    dom: 'Pican<PERSON>'
  },
  adults: {
    en: 'Adults',
    es: 'Adultos',
    dom: 'Adultos'
  },
  couple: {
    en: 'Couple',
    es: 'Parejas',
    dom: 'Parejas'
  },
  pickTheLevel: {
    en: 'Pick the level',
    es: 'Elige el nivel',
    dom: 'Elige el nivel'
  },
  chill: {
    en: 'Chill',
    es: 'Relax',
    dom: 'Relax'
  },
  daring: {
    en: 'Daring',
    es: 'Atrevido',
    dom: 'Atrevido'
  },
  wild: {
    en: 'Wild',
    es: 'Picante',
    dom: 'Picante'
  },
  noLimits: {
    en: 'No Limits',
    es: 'Sin Límites',
    dom: 'Sin Límites'
  },
  instructionsHeading: {
    en: 'Tap In!',
    es: 'Dale tap',
    dom: 'Dale tap'
  },
  instructionsSubtitle: {
    en: 'Place 2+ fingers to start.',
    es: 'Coloca 2+ dedos para empezar',
    dom: 'Pon 2+ dedo\' pa\' empezal'
  },
  modalQuestion: {
    en: 'Answer',
    es: 'Responde',
    dom: 'Responde'
  },
  modalChallenge: {
    en: 'Dare',
    es: 'Reto',
    dom: 'Reto'
  },
  skipButton: {
    en: 'Skip',
    es: 'Saltar',
    dom: 'Saltar'
  },
  resetButton: {
    en: 'Done!',
    es: '¡Hecho!',
    dom: '¡Listo!'
  },
  settingTitle: {
    en: 'Settings',
    es: 'Ajustes',
    dom: 'Ajustes'
  },
  settingLanguage: {
    en: 'Language',
    es: 'Idioma',
    dom: 'Idioma'
  },
  settingNotifications: {
    en: 'Alerts',
    es: 'Alertas',
    dom: 'Alertas'
  },
  settingEffects: {
    en: 'Effects',
    es: 'Efectos',
    dom: 'Efectos'
  },
  soundEffectSounds: {
    en: 'Sounds',
    es: 'Sonidos',
    dom: 'Sonidos'
  },
  soundEffectVibration: {
    en: 'Vibration',
    es: 'Vibración',
    dom: 'Vibración'
  },
  soundEffectsAllOn: {
    en: 'All On',
    es: 'Todos',
    dom: 'Todos'
  },
  soundEffectsAllOff: {
    en: 'None',
    es: 'Ninguno',
    dom: 'Ninguno'
  },
  soundEffectsSomeOn: {
    en: 'Some On',
    es: 'No todos',
    dom: 'No todos'
  },
  notificationsNewUpdate: {
    en: 'New Content',
    es: 'Nuevo contenido',
    dom: 'Nuevo contenido'
  },
  notificationsPromotions: {
    en: 'Promotions',
    es: 'Promociones',
    dom: 'Promociones'
  },
  notificationsAllOff: {
    en: 'None',
    es: 'Ninguna',
    dom: 'Ninguna'
  },
  notificationsOn: {
    en: 'On',
    es: 'Activadas',
    dom: 'Activadas'
  },
  notificationsOff: {
    en: 'Off',
    es: 'Desactivadas',
    dom: 'Desactivadas'
  },
  languageEnglish: {
    en: 'English',
    es: 'Inglés',
    dom: 'Inglés'
  },
  languageSpanish: {
    en: 'Spanish',
    es: 'Español',
    dom: 'Español'
  },
  languageDominican: {
    en: 'Dominican',
    es: 'Dominicano',
    dom: 'Dominicano'
  },
  supportLink: {
    en: 'Support',
    es: 'Soporte',
    dom: 'Soporte'
  },
  privacyLink: {
    en: 'Privacy',
    es: 'Privacidad',
    dom: 'Privacidad'
  },
  termsLink: {
    en: 'Terms',
    es: 'Términos',
    dom: 'Términos'
  },
  selectedOption: {
    en: 'Selected',
    es: 'En juego',
    dom: 'En juego'
  },
  noInternetMessage: {
    en: 'Something went wrong loading content. Please try again or contact us.',
    es: 'Ocurrió un error al cargar el contenido. Si sigue pasando, escríbenos.',
    dom: 'Ocurrió un error al cargar el contenido. Si sigue pasando, escríbenos.'
  },
  noInternetMessageButton: {
    en: 'Try Again',
    es: 'Reintentar',
    dom: 'Reintentar'
  },
  noContentMessage: {
    en: 'We couldn\'t download the content. Please try again. If it persists, please contact us.',
    es: 'No pudimos descargar el contenido. Inténtalo de nuevo. Si persiste, contáctanos.',
    dom: 'No pudimos descargar el contenido. Inténtalo de nuevo. Si persiste, contáctanos.'
  },
  noContentMessageButton: {
    en: 'Try Again',
    es: 'Reintentar',
    dom: 'Reintentar'
  },
  loadingAssets: {
    en: 'Almost there...',
    es: 'Ya casi...',
    dom: 'Ya casi...'
  },
  supportTitle: {
    en: 'Support',
    es: 'Soporte',
    dom: 'Soporte'
  },
  contactUs: {
    en: 'Contact Us',
    es: 'Contáctanos',
    dom: 'Contáctanos'
  },
  userIdTitle: {
    en: 'Your User ID',
    es: 'Tu ID de Usuario',
    dom: 'Tu ID de Usuario'
  },
  userIdDescription: {
    en: 'Please include this ID when contacting support.',
    es: 'Incluye este ID cuando contactes a soporte.',
    dom: 'Incluye este ID cuando contactes a soporte.'
  },
  copyUserId: {
    en: 'Copy ID',
    es: 'Copiar ID',
    dom: 'Copiar ID'
  },
  idCopied: {
    en: 'ID copied!',
    es: '¡ID copiado!',
    dom: '¡ID copiado!'
  },
  inGameBannerTitle: {
    en: 'Want smth spicier?',
    es: '¿Algo más picante?',
    dom: '¿Algo ma\' fuerte?'
  },
  inGameBannerButton: {
    en: 'Try 4 Free',
    es: 'Probar',
    dom: 'Probar'
  },
  inGameBannerSequentialTitle: {
    en: 'Want smth spicier?',
    es: '¿Algo más picante?',
    dom: '¿Algo ma\' fuerte?'
  },
  inGameBannerSequentialButton: {
    en: 'Subscribe',
    es: 'Suscribete',
    dom: 'Suscribete'
  },
  // Penalty
  refurseButton: {
    en: 'Refuse',
    es: 'Me niego',
    dom: 'Baraja'
  },
  penaltyDialogTitle: {
    en: 'Penalty',
    es: 'Castigo',
    dom: 'Castigo'
  },
  penaltyDialogButton: {
    en: 'Next Match',
    es: 'Seguir jugando',
    dom: 'Seguir jugando'
  },
  penaltyNone: {
    en: 'None',
    es: 'Ninguno',
    dom: 'Ninguno'
  },
  penaltyNotAvailable: {
    en: 'No penalties available',
    es: 'No hay castigos disponibles',
    dom: 'No hay castigos disponibles'
  },
  penaltyNotSelected: {
    en: 'No penalty selected.',
    es: 'No se seleccionó castigo.',
    dom: 'No se seleccionó castigo.'
  },
  penaltySelected: {
    en: 'selected',
    es: 'en juego',
    dom: 'en juego'
  },
  penaltyCalloutText: {
    en: 'Choose a penalty for avoiding a dare or question.',
    es: 'Elige un castigo por evitar un reto o una pregunta.',
    dom: 'Elige un castigo por barajar un reto o pregunta.'
  },
  // Store Review
  storeReviewBannerTitle: {
    en: 'Love the game?',
    es: '¿Te diviertes?',
    dom: '¿Te diviertes?'
  },
  storeReviewBannerButton: {
    en: 'Rate Us',
    es: 'Califícanos',
    dom: 'Califícanos'
  },
  // Watch Ads
  watchAdsTitle: {
    en: 'Watch Ads',
    es: 'Anuncios',
    dom: 'Anuncios'
  },
  watchAdsButton: {
    en: 'Watch Ads',
    es: 'Ver Anuncios',
    dom: 'Ver Anuncios'
  },
  watchAdsCallout: {
    en: 'To unlock access to premium, watch these ads.',
    es: 'Para desbloquear el contenido premium, ve estos anuncios.',
    dom: 'Para desbloquear el contenido premium, ve estos anuncios.'
  },
  watchAdsCalloutUnlocked: {
    en: 'You unlocked access to premium for a few minutes.',
    es: 'Desbloqueaste el contenido premium por unos minutos.',
    dom: 'Desbloqueaste el contenido premium por unos minutos.'
  },
  watchAdsAdText: {
    en: 'Ad',
    es: 'Anuncio',
    dom: 'Anuncio'
  },
  watchAdsPending: {
    en: 'Pending',
    es: 'Pendiente',
    dom: 'Pendiente'
  },
  watchAdsWatched: {
    en: 'Watched',
    es: 'Visto',
    dom: 'Visto'
  },
  getPremium: {
    en: 'GET PREMIUM',
    es: 'OBTENER PREMIUM',
    dom: 'OBTENER PREMIUM'
  },
  free: {
    en: 'free',
    es: 'gratis',
    dom: 'gratis'
  },
  // Push Notifications
  pushNotificationBannerTitle: {
    en: 'New Dares Soon!',
    es: '¡Contenido nuevo!',
    dom: '¡Contenido nuevo!'
  },
  pushNotificationBannerButton: {
    en: 'Get Notified',
    es: 'Avísame',
    dom: 'Avísame'
  },
  // Push Notification Settings Navigation
  pushNotificationDisabledTitle: {
    en: 'Notifications Disabled',
    es: 'Notificaciones Desactivadas',
    dom: 'Notificaciones Desactivadas'
  },
  pushNotificationDisabledMessage: {
    en: 'To receive notifications about new content and features, please enable notifications in your device settings.',
    es: 'Para recibir notificaciones sobre nuevo contenido y funciones, activa las notificaciones en la configuración de tu dispositivo.',
    dom: 'Pa\' recibir notificaciones sobre contenido nuevo y funciones, activa las notificaciones en la configuración de tu dispositivo.'
  },
  pushNotificationOpenSettings: {
    en: 'Open Settings',
    es: 'Abrir Configuración',
    dom: 'Abrir Configuración'
  },
  pushNotificationCancel: {
    en: 'Cancel',
    es: 'Cancelar',
    dom: 'Cancelar'
  },
  pushNotificationSettingsError: {
    en: 'Unable to open settings. Please enable notifications manually in your device settings.',
    es: 'No se pudo abrir la configuración. Activa las notificaciones manualmente en la configuración de tu dispositivo.',
    dom: 'No se pudo abrir la configuración. Activa las notificaciones manualmente en la configuración de tu dispositivo.'
  },
  // What's New
  whatsNewTitle: {
    en: 'What\'s New',
    es: '¡Lo nuevo!',
    dom: '¡Lo nuevo!'
  },
  watchVideo: {
    en: 'Watch Video',
    es: 'Ver Video',
    dom: 'Ver Video'
  },
  updateApp: {
    en: 'Please update the app.',
    es: 'Actualiza la app.',
    dom: 'Actualiza la app.'
  },
  whatsNewEmpty: {
    en: 'No updates available',
    es: 'No hay actualizaciones',
    dom: 'No hay actualizaciones'
  },
  whatsNewEmptySubtext: {
    en: 'Check back later for new features and improvements!',
    es: 'Vuelve de nuevo más tarde por nuevas funciones y mejoras!',
    dom: 'Vuelve de nuevo más tarde por nuevas funciones y mejoras!'
  },
  whatsNewNoInternetTitle: {
    en: 'No Internet Connection',
    es: 'Sin Conexión a Internet',
    dom: 'Sin Conexión a Internet'
  },
  whatsNewNoInternetMessage: {
    en: 'Please connect to the internet to view What\'s New.',
    es: 'Conéctate a internet para ver las novedades.',
    dom: 'Conéctate a internet pa\' ver las novedades.'
  },
  ok: {
    en: 'OK',
    es: 'OK',
    dom: 'OK'
  },
  toastLoadingTitle: {
    en: 'Getting new content...',
    es: 'Obteniendo nuevo contenido...',
    dom: 'Obteniendo nuevo contenido...'
  },
  toastLoadingLabel: {
    en: 'Please wait...',
    es: 'Por favor, espera...',
    dom: 'Por favor, espera...'
  },
  toastErrorTitle: {
    en: 'Oops, we couldn\'t grab the game content!',
    es: 'Ups, no pudimos obtener el contenido del juego!',
    dom: 'Ups, no pudimos obtener el contenido del juego!'
  },
  toastErrorLabel: {
    en: 'Please check your internet connection.',
    es: 'Verifica tu conexión a internet.',
    dom: 'Verifica tu conexión a internet.'
  },
  toastErrorButton: {
    en: 'Retry',
    es: 'Reintentar',
    dom: 'Reintentar'
  },
  toastSuccessTitle: {
    en: 'You are all set! Enjoy!',
    es: '¡Estás listo!',
    dom: '¡Estás listo!'
  },
  toastSuccessLabel: {
    en: 'Thanks for your patient.',
    es: 'Gracias por tu paciencia.',
    dom: 'Gracias por tu paciencia.'
  }
};

// Context definition
interface LanguageContextProps {
  language: LanguageType;
  setAppLanguage: (lang: LanguageType) => void;
  t: (key: keyof typeof translations) => string;
}

export const LanguageContext = createContext<LanguageContextProps>({
  language: 'en',
  setAppLanguage: () => {},
  t: (key) => translations[key]?.en ?? key,
});

// Hook to use language context
export const useLanguage = () => useContext(LanguageContext);

// Helper function to get the current device language
export const getDeviceLanguage = (): LanguageType => {
  try {
    // Use getLocales() instead of deprecated locale property
    const locales = Localization.getLocales();
    const languageCode = locales.length > 0 ? locales[0].languageCode || 'en' : 'en';

    // Check if the language code is one of our supported languages
    if (languageCode && ['en', 'es', 'dom'].includes(languageCode)) {
      return languageCode as LanguageType;
    }

    // Special case for Dominican Republic
    if (languageCode === 'es' && locales.length > 0 && locales[0].regionCode === 'DO') {
      return 'dom';
    }

    return 'en'; // Default to English
  } catch {
    return 'en'; // Default to English if there's an error
  }
};

// Provider component
export const LanguageProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<LanguageType>('en');

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const stored = await AsyncStorage.getItem('appLanguage');
        console.log('Stored language from AsyncStorage:', stored);

        if (stored === 'en' || stored === 'es' || stored === 'dom') {
          console.log('Using stored language:', stored);
          setLanguage(stored as LanguageType);
        } else {
          // Use the getDeviceLanguage helper function
          const deviceLanguage = getDeviceLanguage();
          console.log('Using device language:', deviceLanguage);
          setLanguage(deviceLanguage);
        }
      } catch (error) {
        console.error('Error loading language:', error);
        setLanguage('en');
      }
    };
    loadLanguage();
  }, []);

  const setAppLanguage = async (lang: LanguageType) => {
    setLanguage(lang);
    try {
      await AsyncStorage.setItem('appLanguage', lang);
    } catch (err) {
      console.warn('Failed to store language', err);
    }
  };

  const t = (key: keyof typeof translations): string =>
    translations[key]?.[language] ?? translations[key]?.en ?? key;

  return (
    <LanguageContext.Provider value={{ language, setAppLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};